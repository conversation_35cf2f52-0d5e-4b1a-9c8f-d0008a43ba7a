package message_forward

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/tasks"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// MessageForwardTaskHandler 消息转发任务处理器
type MessageForwardTaskHandler struct {
	*tasks.BaseTaskHandler

	// 服务依赖（运行时注入）
	forwardService      types.ForwardRuleManager
	notificationService types.ProductNotificationService
}

// NewMessageForwardTaskHandler 创建新的消息转发任务处理器
func NewMessageForwardTaskHandler() *MessageForwardTaskHandler {
	handler := &MessageForwardTaskHandler{
		BaseTaskHandler: tasks.NewBaseTaskHandler(
			"message_forward",
			"处理频道间的延迟消息转发任务",
		),
	}

	return handler
}

// SetForwardService 设置转发服务（运行时注入）
func (h *MessageForwardTaskHandler) SetForwardService(forwardService types.ForwardRuleManager) {
	h.forwardService = forwardService
}

// SetNotificationService 设置通知服务（运行时注入）
func (h *MessageForwardTaskHandler) SetNotificationService(notificationService types.ProductNotificationService) {
	h.notificationService = notificationService
	logger.Debug("通知服务已注入到消息转发任务处理器")
}

// SetTemplateServices 设置模板服务（向后兼容，已弃用）
func (h *MessageForwardTaskHandler) SetTemplateServices(manager interface{}, renderer interface{}) {
	logger.Warn("SetTemplateServices已弃用，消息转发现在使用通知服务")
}

// getForwardService 获取转发服务（优先使用注入的服务，否则从客户端获取）
func (h *MessageForwardTaskHandler) getForwardService(client *types.Client) types.ForwardRuleManager {
	// 优先使用注入的服务
	if h.forwardService != nil {
		logger.Debug("使用注入的转发服务")
		return h.forwardService
	}

	// 从客户端服务容器获取
	if client != nil && client.Services != nil && client.Services.ForwardService != nil {
		logger.Debug("从客户端服务容器获取转发服务")
		return client.Services.ForwardService
	}

	logger.Debug("无法获取转发服务")
	return nil
}

// Handle 处理消息转发任务
func (h *MessageForwardTaskHandler) Handle(ctx context.Context, client *types.Client, data any) error {
	startTime := time.Now()

	// 检查转发服务可用性
	forwardServiceAvailable := h.getForwardService(client) != nil

	logger.Info("🚀 开始处理消息转发任务",
		"handler", h.GetType(),
		"forward_service_injected", h.forwardService != nil,
		"forward_service_available", forwardServiceAvailable)

	// 解析任务数据
	task, err := ParseTaskFromData(data)
	if err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	// 验证任务数据
	if err := task.Validate(); err != nil {
		logger.Error("任务数据验证失败",
			"task_id", task.ID,
			"original_message", task.OriginalMessage,
			"original_message_length", len(task.OriginalMessage),
			"source_channel", task.SourceChannel,
			"target_channels", task.TargetChannels,
			"author_id", task.AuthorID,
			"guild_id", task.GuildID,
			"error", err)
		return fmt.Errorf("任务数据验证失败: %w", err)
	}

	logger.Info("处理消息转发任务",
		"task_id", task.ID,
		"mapping", task.MappingName,
		"source_channel", task.SourceChannel,
		"target_channels", len(task.TargetChannels),
		"original_message_length", len(task.OriginalMessage))

	// 直接使用原始消息内容，如果为空则使用占位符
	processedMessage := task.OriginalMessage
	if strings.TrimSpace(processedMessage) == "" {
		processedMessage = "[无文本内容]" // 默认占位符
	}

	task.ProcessedMessage = processedMessage

	logger.Debug("消息处理完成",
		"task_id", task.ID,
		"original_length", len(task.OriginalMessage),
		"processed_length", len(task.ProcessedMessage),
		"processed_message", task.ProcessedMessage)

	// 发送消息到目标频道
	if err := h.sendToTargetChannels(ctx, client, task); err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	// 记录处理完成
	duration := time.Since(startTime)
	logger.Info("消息转发任务处理完成",
		"task_id", task.ID,
		"duration", duration,
		"target_channels", len(task.TargetChannels))

	return nil
}

// Validate 验证任务数据
func (h *MessageForwardTaskHandler) Validate(data any) error {
	if data == nil {
		return types.ErrInvalidTaskData
	}

	// 尝试解析任务数据
	_, err := ParseTaskFromData(data)
	return err
}

// convertProductsToEmbeds 将ProductItem数组转换为Discord embed，使用通知服务
func (h *MessageForwardTaskHandler) convertProductsToEmbeds(ctx context.Context, products []*types.ProductItem, channelID string) []*discordgo.MessageEmbed {
	embeds := make([]*discordgo.MessageEmbed, 0, len(products))

	// 检查通知服务是否可用
	if h.notificationService == nil {
		logger.Warn("通知服务未注入，使用回退方法")
		return h.convertProductsToEmbedsLegacy(products)
	}

	for _, product := range products {
		if product == nil {
			continue
		}

		// 使用通知服务生成 embed
		embed := h.createEmbedWithNotificationService(ctx, product, channelID)
		if embed != nil {
			embeds = append(embeds, embed)
		}
	}

	return embeds
}

// createEmbedWithNotificationService 使用通知服务创建 embed
func (h *MessageForwardTaskHandler) createEmbedWithNotificationService(ctx context.Context, product *types.ProductItem, channelID string) *discordgo.MessageEmbed {
	// 使用通知服务渲染（但不发送）
	// 这里我们需要一个只渲染不发送的方法
	// 暂时使用回退方法
	logger.Debug("通知服务暂不支持仅渲染模式，使用回退方法", "product_platform", product.Platform)
	return h.createEmbedByPlatformSimple(product)
}

// createEmbedByPlatformSimple 简化的平台embed创建
func (h *MessageForwardTaskHandler) createEmbedByPlatformSimple(product *types.ProductItem) *discordgo.MessageEmbed {
	if product == nil {
		return nil
	}

	// 基础embed结构
	embed := &discordgo.MessageEmbed{
		Title: product.Title,
		URL:   product.URL,
		Color: h.getPlatformColor(product.Platform),
	}

	// 设置缩略图
	if product.ThumbnailURL != nil && *product.ThumbnailURL != "" {
		embed.Thumbnail = &discordgo.MessageEmbedThumbnail{URL: *product.ThumbnailURL}
	}

	// 添加基础字段
	fields := []*discordgo.MessageEmbedField{}

	if product.Price != "" {
		fields = append(fields, &discordgo.MessageEmbedField{
			Name:   "价格",
			Value:  product.Price,
			Inline: true,
		})
	}

	if product.Platform != "" {
		fields = append(fields, &discordgo.MessageEmbedField{
			Name:   "平台",
			Value:  product.Platform,
			Inline: true,
		})
	}

	if product.Stock > 0 {
		fields = append(fields, &discordgo.MessageEmbedField{
			Name:   "库存",
			Value:  fmt.Sprintf("%d", product.Stock),
			Inline: true,
		})
	}

	embed.Fields = fields

	// 设置页脚
	embed.Footer = &discordgo.MessageEmbedFooter{
		Text: fmt.Sprintf("来自 %s", product.Platform),
	}

	return embed
}

// getPlatformColor 获取平台颜色
func (h *MessageForwardTaskHandler) getPlatformColor(platform string) int {
	switch strings.ToLower(platform) {
	case "aliexpress":
		return 0xFF6600 // 橙红色
	case "popmart":
		return 0xFF69B4 // 粉色
	case "amazon":
		return 0xFF9900 // 亚马逊橙
	default:
		return 0x7289DA // Discord蓝
	}
}

// convertProductsToEmbedsLegacy 回退方法：使用简化逻辑
func (h *MessageForwardTaskHandler) convertProductsToEmbedsLegacy(products []*types.ProductItem) []*discordgo.MessageEmbed {
	embeds := make([]*discordgo.MessageEmbed, 0, len(products))

	for _, product := range products {
		if product == nil {
			continue
		}

		// 使用简化的embed创建
		embed := h.createEmbedByPlatformSimple(product)
		if embed != nil {
			embeds = append(embeds, embed)
		}
	}

	return embeds
}

// replaceTemplateVarsLegacy 替换模板变量（回退方法）
func (h *MessageForwardTaskHandler) replaceTemplateVarsLegacy(template string, product *types.ProductItem) string {
	if template == "" {
		return ""
	}

	// 简化的变量映射
	variables := map[string]interface{}{
		"title":       product.Title,
		"price":       product.Price,
		"platform":    product.Platform,
		"url":         product.URL,
		"productId":   product.ProductID,
		"stock":       product.Stock,
		"description": product.Description,
	}

	result := template

	// 简单的变量替换
	for key, value := range variables {
		placeholder := "{" + key + "}"
		valueStr := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, valueStr)
	}

	return result
}

// convertEmbedsFromMap 将map格式的嵌入消息转换为discordgo.MessageEmbed
func (h *MessageForwardTaskHandler) convertEmbedsFromMap(embedsData []map[string]interface{}) []*discordgo.MessageEmbed {
	embeds := make([]*discordgo.MessageEmbed, len(embedsData))
	for i, embedData := range embedsData {
		embed := &discordgo.MessageEmbed{}

		// 基本字段
		if title, ok := embedData["title"].(string); ok {
			embed.Title = title
		}
		if description, ok := embedData["description"].(string); ok {
			embed.Description = description
		}
		if url, ok := embedData["url"].(string); ok {
			embed.URL = url
		}
		if color, ok := embedData["color"].(float64); ok {
			embed.Color = int(color)
		} else if color, ok := embedData["color"].(int); ok {
			embed.Color = color
		}
		if timestamp, ok := embedData["timestamp"].(string); ok {
			embed.Timestamp = timestamp
		}

		// 字段
		if fieldsRaw, ok := embedData["fields"].([]interface{}); ok {
			fields := make([]*discordgo.MessageEmbedField, len(fieldsRaw))
			for j, fieldRaw := range fieldsRaw {
				if fieldMap, ok := fieldRaw.(map[string]interface{}); ok {
					field := &discordgo.MessageEmbedField{}
					if name, ok := fieldMap["name"].(string); ok {
						field.Name = name
					}
					if value, ok := fieldMap["value"].(string); ok {
						field.Value = value
					}
					if inline, ok := fieldMap["inline"].(bool); ok {
						field.Inline = inline
					}
					fields[j] = field
				}
			}
			embed.Fields = fields
		}

		// 图片
		if imageRaw, ok := embedData["image"].(map[string]interface{}); ok {
			if imageURL, ok := imageRaw["url"].(string); ok {
				embed.Image = &discordgo.MessageEmbedImage{URL: imageURL}
			}
		}

		// 缩略图
		if thumbnailRaw, ok := embedData["thumbnail"].(map[string]interface{}); ok {
			if thumbnailURL, ok := thumbnailRaw["url"].(string); ok {
				embed.Thumbnail = &discordgo.MessageEmbedThumbnail{URL: thumbnailURL}
			}
		}

		// 作者
		if authorRaw, ok := embedData["author"].(map[string]interface{}); ok {
			author := &discordgo.MessageEmbedAuthor{}
			if name, ok := authorRaw["name"].(string); ok {
				author.Name = name
			}
			if url, ok := authorRaw["url"].(string); ok {
				author.URL = url
			}
			if iconURL, ok := authorRaw["icon_url"].(string); ok {
				author.IconURL = iconURL
			}
			embed.Author = author
		}

		// 页脚
		if footerRaw, ok := embedData["footer"].(map[string]interface{}); ok {
			footer := &discordgo.MessageEmbedFooter{}
			if text, ok := footerRaw["text"].(string); ok {
				footer.Text = text
			}
			if iconURL, ok := footerRaw["icon_url"].(string); ok {
				footer.IconURL = iconURL
			}
			embed.Footer = footer
		}

		embeds[i] = embed
	}
	return embeds
}

// sendToTargetChannels 发送消息到目标频道
func (h *MessageForwardTaskHandler) sendToTargetChannels(_ context.Context, client *types.Client, task *MessageForwardTask) error {
	if client.Session == nil {
		return fmt.Errorf("discord session 未初始化")
	}

	// 检查处理后的消息是否为空
	if strings.TrimSpace(task.ProcessedMessage) == "" {
		logger.Error("处理后的消息为空，无法发送",
			"task_id", task.ID,
			"original_message", task.OriginalMessage,
			"processed_message", task.ProcessedMessage)
		return fmt.Errorf("处理后的消息为空，Discord不允许发送空消息")
	}

	successCount := 0
	var lastError error

	for _, channelID := range task.TargetChannels {
		logger.Debug("发送消息到频道", "channel", channelID, "task_id", task.ID, "has_products", len(task.Products) > 0)

		// 构建消息发送数据
		messageSend := &discordgo.MessageSend{}

		// 优先处理ProductItem（新格式）
		if len(task.Products) > 0 {
			// 从ProductItem生成embed
			embeds := h.convertProductsToEmbeds(context.Background(), task.Products, channelID)
			if len(embeds) > 0 {
				messageSend.Embeds = embeds
				messageSend.Content = "" // 有embed时不发送文本内容
				logger.Debug("使用ProductItem生成embed", "count", len(embeds))
			} else {
				// 如果无法生成embed，发送处理后的文本内容
				messageSend.Content = task.ProcessedMessage
			}
		} else {
			// 向后兼容：检查是否有旧格式的嵌入消息
			hasLegacyEmbeds := task.EmbedsJSON != "" || len(task.Embeds) > 0

			if hasLegacyEmbeds {
				var embeds []*discordgo.MessageEmbed

				// 优先使用JSON格式的嵌入消息
				if task.EmbedsJSON != "" {
					var embedsFromJSON []*discordgo.MessageEmbed
					err := json.Unmarshal([]byte(task.EmbedsJSON), &embedsFromJSON)
					if err != nil {
						logger.Error("嵌入消息JSON反序列化失败", "error", err, "json", task.EmbedsJSON)
						// 回退到旧格式
						if len(task.Embeds) > 0 {
							embeds = h.convertEmbedsFromMap(task.Embeds)
						}
					} else {
						embeds = embedsFromJSON
						logger.Debug("使用JSON格式嵌入消息（向后兼容）", "count", len(embeds))
					}
				} else if len(task.Embeds) > 0 {
					// 使用旧格式的嵌入消息
					embeds = h.convertEmbedsFromMap(task.Embeds)
					logger.Debug("使用旧格式嵌入消息（向后兼容）", "count", len(embeds))
				}

				if len(embeds) > 0 {
					messageSend.Embeds = embeds
					messageSend.Content = ""
				} else {
					messageSend.Content = task.ProcessedMessage
				}
			} else {
				// 没有任何embed数据时，发送处理后的文本内容
				messageSend.Content = task.ProcessedMessage
			}
		}

		// 发送消息
		_, err := client.Session.ChannelMessageSendComplex(channelID, messageSend)
		if err != nil {
			logger.Error("发送消息到频道失败",
				"error", err,
				"channel", channelID,
				"task_id", task.ID)
			lastError = err
			continue
		}

		successCount++
		logger.Debug("消息发送成功", "channel", channelID, "task_id", task.ID)
	}

	// 检查发送结果
	if successCount == 0 {
		return fmt.Errorf("所有频道消息发送失败，最后错误: %w", lastError)
	}

	if successCount < len(task.TargetChannels) {
		logger.Warn("部分频道消息发送失败",
			"success", successCount,
			"total", len(task.TargetChannels),
			"task_id", task.ID)
	}

	logger.Info("消息转发完成",
		"task_id", task.ID,
		"success_count", successCount,
		"total_channels", len(task.TargetChannels))

	return nil
}
